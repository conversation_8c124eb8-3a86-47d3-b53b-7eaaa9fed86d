import { create } from 'zustand';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { User, AuthState } from '../types/auth';

interface AuthStoreState extends AuthState {
  // Methods
  initialize: () => Promise<void>;
  login: (token: string, userData: User) => Promise<void>;
  logout: () => Promise<void>;
}

export const useAuthStore = create<AuthStoreState>((set) => ({
  isLoading: true,
  userToken: null,
  userId: null,
  user: null,

  initialize: async () => {
    try {
      const token = await AsyncStorage.getItem('access_token');
      if (token) {
        const userJson = await AsyncStorage.getItem('user');
        if (userJson) {
          const userData = JSON.parse(userJson);
          set({ user: userData, userId: userData.id });
        }
        set({ userToken: token });
      }
    } catch (error) {
      console.error('Error restoring auth state', error);
    } finally {
      set({ isLoading: false });
    }
  },

  login: async (token: string, userData: User) => {
    try {
      await AsyncStorage.setItem('access_token', token);
      await AsyncStorage.setItem('role', userData.role.name);
      await AsyncStorage.setItem('user', JSON.stringify(userData));

      set({
        userToken: token,
        user: userData,
        userId: userData.id
      });
    } catch (error) {
      console.error('Error storing auth data', error);
      throw error;
    }
  },

  logout: async () => {
    try {
      await AsyncStorage.multiRemove(['access_token', 'role', 'user']);
      set({
        userToken: null,
        user: null,
        userId: null
      });
    } catch (error) {
      console.error('Error during logout', error);
      throw error;
    }
  }
}));