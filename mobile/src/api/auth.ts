import http from "../services/http";
import type {
  LoginData,
  LoginResponse,
  RegisterData,
  RegisterResponse
} from "../types/auth";

// Re-export types for backward compatibility
export type { LoginData, LoginResponse, RegisterData, RegisterResponse };

export const loginApi = {
  login: async (userData: LoginData): Promise<LoginResponse> => {
    try {
      const response = await http.post("/auth/login", userData);
      return response.data;
    } catch (error: any) {
      console.error("Login API error:", error.response?.data || error.message);
      throw error.response?.data || error;
    }
  },
};

export const registerApi = {
  register: async (userData: RegisterData): Promise<RegisterResponse> => {
    try {
      const response = await http.post("/auth/register", userData);
      return response.data;
    } catch (error: any) {
      console.error(
        "Register API error:",
        error.response?.data || error.message
      );
      throw error.response?.data || error;
    }
  },
};
